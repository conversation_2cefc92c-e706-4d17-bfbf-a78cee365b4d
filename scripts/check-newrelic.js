#!/usr/bin/env node

/**
 * New Relic Diagnostic Script
 *
 * This script checks if New Relic is properly configured and can send data.
 * Run this script to verify your New Relic integration.
 */

console.log('🔍 New Relic Diagnostic Check\n');

// Check environment variables
console.log('📋 Environment Variables:');
console.log(`NEW_RELIC_LICENSE_KEY: ${process.env.NEW_RELIC_LICENSE_KEY ? '✅ Set (length: ' + process.env.NEW_RELIC_LICENSE_KEY.length + ')' : '❌ Not set'}`);
console.log(`NEW_RELIC_APP_NAME: ${process.env.NEW_RELIC_APP_NAME || '❌ Not set'}`);
console.log(`NEW_RELIC_LOG_LEVEL: ${process.env.NEW_RELIC_LOG_LEVEL || 'info (default)'}`);
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'development (default)'}`);
console.log('');

// Try to load New Relic
let newrelic = null;
try {
  console.log('🚀 Attempting to load New Relic...');

  if (!process.env.NEW_RELIC_LICENSE_KEY) {
    console.log('❌ Cannot load New Relic: License key not set');
    process.exit(1);
  }

  if (process.env.NEW_RELIC_LICENSE_KEY === 'dummy_key_for_build') {
    console.log('⚠️  Using dummy license key - New Relic will not work');
    process.exit(1);
  }

  newrelic = require('newrelic');
  console.log('✅ New Relic loaded successfully');

} catch (error) {
  console.log('❌ Failed to load New Relic:', error.message);
  process.exit(1);
}

// Check New Relic agent status
console.log('\n📊 New Relic Agent Status:');
try {
  const agent = newrelic.agent;
  console.log(`Agent enabled: ${agent.config.agent_enabled ? '✅ Yes' : '❌ No'}`);
  console.log(`App name: ${agent.config.app_name}`);
  console.log(`License key: ${agent.config.license_key ? '✅ Set' : '❌ Not set'}`);
  console.log(`Logging enabled: ${agent.config.logging.enabled ? '✅ Yes' : '❌ No'}`);
  console.log(`Log level: ${agent.config.logging.level}`);
  console.log(`Log forwarding enabled: ${agent.config.application_logging.forwarding.enabled ? '✅ Yes' : '❌ No'}`);
  console.log(`Distributed tracing: ${agent.config.distributed_tracing.enabled ? '✅ Yes' : '❌ No'}`);
} catch (error) {
  console.log('❌ Cannot read agent status:', error.message);
}

// Test custom attributes
console.log('\n🧪 Testing Custom Attributes:');
try {
  newrelic.addCustomAttribute('test_attribute', 'diagnostic_check');
  console.log('✅ Custom attributes working');
} catch (error) {
  console.log('❌ Custom attributes failed:', error.message);
}

// Test custom events
console.log('\n🎯 Testing Custom Events:');
try {
  newrelic.recordCustomEvent('DiagnosticTest', {
    timestamp: new Date().toISOString(),
    test_type: 'diagnostic_check',
    success: true
  });
  console.log('✅ Custom events working');
} catch (error) {
  console.log('❌ Custom events failed:', error.message);
}

// Test error recording
console.log('\n🚨 Testing Error Recording:');
try {
  const testError = new Error('Test error for New Relic diagnostic');
  newrelic.noticeError(testError, { test: true });
  console.log('✅ Error recording working');
} catch (error) {
  console.log('❌ Error recording failed:', error.message);
}

// Test metrics
console.log('\n📈 Testing Metrics:');
try {
  newrelic.recordMetric('Custom/DiagnosticTest', 1);
  console.log('✅ Metrics recording working');
} catch (error) {
  console.log('❌ Metrics recording failed:', error.message);
}

// Test log forwarding
console.log('\n📝 Testing Log Forwarding:');
try {
  // Create a test log entry
  console.log('TEST LOG: This is a test log entry for New Relic log forwarding verification');
  console.error('TEST ERROR LOG: This is a test error log entry for New Relic');

  // Use structured logging
  const testLogData = {
    level: 'info',
    message: 'New Relic diagnostic test log',
    timestamp: new Date().toISOString(),
    service: 'invoice-matching-api',
    test: true
  };

  console.log(JSON.stringify(testLogData));
  console.log('✅ Log forwarding test completed');
} catch (error) {
  console.log('❌ Log forwarding test failed:', error.message);
}

console.log('\n🎉 Diagnostic check completed!');
console.log('\n📋 Next Steps:');
console.log('1. Check your New Relic dashboard in 1-2 minutes for:');
console.log('   - Application data under APM & Services');
console.log('   - Custom event "DiagnosticTest" under Events');
console.log('   - Test error under Errors');
console.log('   - Custom metric "Custom/DiagnosticTest" under Metrics');
console.log('   - Log entries under Logs');
console.log('');
console.log('2. If data is missing:');
console.log('   - Verify your license key is correct');
console.log('   - Check network connectivity to New Relic');
console.log('   - Review application logs for New Relic errors');
console.log('   - Ensure your New Relic account has the right permissions');

// Keep the process alive for a moment to ensure data is sent
setTimeout(() => {
  console.log('\n✨ Diagnostic script finished. Check your New Relic dashboard!');
  process.exit(0);
}, 2000);
