#!/usr/bin/env node

/**
 * New Relic Log Forwarding Test
 * 
 * This script specifically tests log forwarding to New Relic.
 * It generates various types of log entries to verify they appear in New Relic Logs.
 */

console.log('📝 New Relic Log Forwarding Test\n');

// Load New Relic first
let newrelic = null;
try {
  if (!process.env.NEW_RELIC_LICENSE_KEY || process.env.NEW_RELIC_LICENSE_KEY === 'dummy_key_for_build') {
    console.log('❌ Valid New Relic license key required');
    process.exit(1);
  }
  
  newrelic = require('newrelic');
  console.log('✅ New Relic loaded');
} catch (error) {
  console.log('❌ Failed to load New Relic:', error.message);
  process.exit(1);
}

// Test different log levels and formats
console.log('\n🧪 Testing different log formats...\n');

// 1. Simple console logs
console.log('TEST LOG 1: Simple info message');
console.warn('TEST LOG 2: Warning message');
console.error('TEST LOG 3: Error message');

// 2. Structured JSON logs (like Pino)
const structuredLog = {
  level: 30,
  time: Date.now(),
  pid: process.pid,
  hostname: require('os').hostname(),
  msg: 'TEST LOG 4: Structured JSON log entry',
  service: 'invoice-matching-api',
  component: 'log-forwarding-test',
  test_id: 'structured_log_test',
  custom_field: 'test_value'
};

console.log(JSON.stringify(structuredLog));

// 3. Application-specific log format
const appLog = {
  level: 'INFO',
  time: new Date().toISOString(),
  pid: process.pid,
  hostname: require('os').hostname(),
  component: 'testLogger',
  event: 'log_forwarding_test',
  msg: 'TEST LOG 5: Application-style log entry',
  metadata: {
    test: true,
    timestamp: Date.now(),
    user_agent: 'New Relic Log Test Script'
  }
};

console.log(JSON.stringify(appLog));

// 4. Error with stack trace
try {
  throw new Error('TEST LOG 6: Test error with stack trace for log forwarding');
} catch (error) {
  console.error('Caught test error:', error.message);
  console.error('Stack trace:', error.stack);
  
  // Also report to New Relic
  newrelic.noticeError(error, { 
    test: true, 
    source: 'log_forwarding_test',
    timestamp: new Date().toISOString()
  });
}

// 5. Invoice processing simulation logs
const invoiceTestLogs = [
  {
    level: 'INFO',
    time: new Date().toISOString(),
    component: 'invoiceProcessor',
    event: 'processing_started',
    msg: 'TEST LOG 7: Invoice processing started',
    invoice_id: 'test-invoice-001',
    request_type: 'invoice'
  },
  {
    level: 'INFO', 
    time: new Date().toISOString(),
    component: 'colorDetection',
    event: 'color_detection_completed',
    msg: 'TEST LOG 8: Color detection completed',
    result: {
      is_colored: true,
      total_pages: 2,
      color_pages: [1, 2]
    }
  },
  {
    level: 'ERROR',
    time: new Date().toISOString(),
    component: 'externalAPI',
    event: 'api_call_failed',
    msg: 'TEST LOG 9: External API call failed',
    error: 'Connection timeout',
    api_endpoint: '/process-document',
    retry_count: 3
  }
];

invoiceTestLogs.forEach(log => {
  console.log(JSON.stringify(log));
});

// 6. High-volume log test
console.log('\n🚀 Generating high-volume logs...');
for (let i = 1; i <= 10; i++) {
  const bulkLog = {
    level: 'INFO',
    time: new Date().toISOString(),
    component: 'bulkTest',
    msg: `TEST LOG BULK ${i}: High-volume log entry ${i}/10`,
    sequence: i,
    batch_id: 'bulk-test-001'
  };
  console.log(JSON.stringify(bulkLog));
}

// 7. Custom New Relic log attributes
console.log('\n🎯 Adding custom New Relic attributes...');
newrelic.addCustomAttribute('log_test_session', Date.now());
newrelic.addCustomAttribute('log_test_type', 'forwarding_verification');

// 8. Record custom event for correlation
newrelic.recordCustomEvent('LogForwardingTest', {
  timestamp: new Date().toISOString(),
  test_session: Date.now(),
  log_count: 15,
  test_types: ['simple', 'structured', 'error', 'bulk'],
  success: true
});

console.log('\n✅ Log forwarding test completed!');
console.log('\n📋 What to check in New Relic:');
console.log('1. Go to New Relic Logs section');
console.log('2. Look for logs with messages starting with "TEST LOG"');
console.log('3. Verify different log levels appear (INFO, WARN, ERROR)');
console.log('4. Check that JSON structure is preserved');
console.log('5. Verify custom attributes are attached');
console.log('6. Look for the "LogForwardingTest" custom event');
console.log('\n⏰ Logs should appear within 1-2 minutes');

// Keep process alive to ensure logs are flushed
setTimeout(() => {
  console.log('\n🎉 Test script finished. Check your New Relic Logs dashboard!');
  process.exit(0);
}, 3000);
