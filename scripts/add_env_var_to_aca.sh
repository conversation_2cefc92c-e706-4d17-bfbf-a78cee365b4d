#!/bin/bash

# Script to add a new environment variable to an Azure Container App
# without overwriting existing ones.

# Usage:
# ./scripts/add_env_var_to_aca.sh <CONTAINER_APP_NAME> <RESOURCE_GROUP_NAME> <ENV_VAR_NAME> <ENV_VAR_VALUE>

# Check if jq is installed
if ! command -v jq &> /dev/null
then
    echo "jq is not installed. Please install it to run this script."
    echo "For macOS: brew install jq"
    echo "For Debian/Ubuntu: sudo apt-get install jq"
    exit 1
fi

# Check for correct number of arguments
if [ "$#" -ne 4 ]; then
    echo "Usage: $0 <CONTAINER_APP_NAME> <RESOURCE_GROUP_NAME> <ENV_VAR_NAME> <ENV_VAR_VALUE>"
    exit 1
fi

CONTAINER_APP_NAME=$1
RESOURCE_GROUP_NAME=$2
ENV_VAR_NAME=$3
ENV_VAR_VALUE=$4

echo "Adding environment variable '$ENV_VAR_NAME' with value '$ENV_VAR_VALUE' to Azure Container App '$CONTAINER_APP_NAME' in resource group '$RESOURCE_GROUP_NAME'..."

# Retrieve existing environment variables
EXISTING_VARS=$(az containerapp show \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --query 'properties.template.containers[0].env' \
  --output json)

# Check if the variable already exists and update it, otherwise add it
if echo "$EXISTING_VARS" | jq -e --arg name "$ENV_VAR_NAME" 'map(select(.name == $name)) | length > 0' > /dev/null; then
    echo "Environment variable '$ENV_VAR_NAME' already exists. Updating its value."
    UPDATED_VARS=$(echo "$EXISTING_VARS" | jq --arg name "$ENV_VAR_NAME" --arg value "$ENV_VAR_VALUE" 'map(if .name == $name then .value = $value else . end)')
else
    echo "Environment variable '$ENV_VAR_NAME' does not exist. Adding it."
    NEW_VAR_JSON='{"name": "'"$ENV_VAR_NAME"'", "value": "'"$ENV_VAR_VALUE"'"}'
    UPDATED_VARS=$(echo "$EXISTING_VARS" | jq --argjson new_var "$NEW_VAR_JSON" '. + [$new_var]')
fi

# Create a temporary file with the updated environment variables JSON
TEMP_ENV_FILE=$(mktemp)
echo "$UPDATED_VARS" > "$TEMP_ENV_FILE"

# Update the container app with the combined list using the temporary file
az containerapp update \
  --name "$CONTAINER_APP_NAME" \
  --resource-group "$RESOURCE_GROUP_NAME" \
  --set "properties.template.containers[0].env=@$TEMP_ENV_FILE"

# Clean up the temporary file
rm "$TEMP_ENV_FILE"

if [ $? -eq 0 ]; then
    echo "Successfully updated environment variables for $CONTAINER_APP_NAME. A new revision is being deployed."
else
    echo "Failed to update environment variables for $CONTAINER_APP_NAME."
    exit 1
fi