# Remote API Integration Tests

This directory contains integration tests that call the remote API endpoints for invoice and tax invoice processing.

## Test Configuration

The tests are configured using two separate configuration files for better organization:

1. **`test-config-invoice.json`** - Contains test cases for regular invoice processing
2. **`test-config-tax-invoice.json`** - Contains test cases for tax invoice processing

### Setting Up Your Configuration

1. Copy the example configuration files:
   ```bash
   cp test-config-invoice.json.example test-config-invoice.json
   cp test-config-tax-invoice.json.example test-config-tax-invoice.json
   ```

2. Create your environment configuration:
   ```bash
   cp test-env.json.example test-env.json
   ```

3. Edit the `test-env.json` file to set your API credentials:
   ```json
   {
      "base_url": "https://your-api-base-url.com",
      "api_key": "your-api-key-here",
      "local_file_base_path": "/path/to/your/test/files"
   }
   ```

> **Note:** Configuration files are excluded from version control via `.gitignore` to prevent committing sensitive information like API keys.

### Configuration Structure

#### Configuration Format

The configuration format provides detailed field-by-field validation:

```json
{
   "tests":[
      {
         "uuid": "unique-test-identifier",
         "input":{
            "file":"invoice/example.pdf",
            "vendor_name":"Company Name",
            "invoice_number":"INV-001",
            "invoice_date":"2024-01-15",
            "invoice_amount":1000000.00,
            "vat_amount":100000.00
         },
         "output":{
            "fields": {
               "vendor_name": {
                  "input_value": "Company Name",
                  "ocr_value": "Company Name",
                  "status": "matched"
               },
               "invoice_number": {
                  "input_value": "INV-001",
                  "ocr_value": "INV-001",
                  "status": "matched"
               }
               // ... other fields
            },
            "summary": {
               "total_fields": 5,
               "matched": 5,
               "mismatched": 0,
               "not_found": 0
            },
            "is_colored": true,
            "color_pages": [1],
            "total_pages": 1
         },
         "focus": false
      }
   ]
}
```

#### Environment Configuration

Create a separate `test-env.json` file:

```json
{
   "base_url": "https://your-api-base-url.com",
   "api_key": "your-api-key-here",
   "local_file_base_path": "/path/to/your/test/files"
}
```



### Using Remote Files vs Local Files

The test supports two ways to provide input files:

1. **Remote Files**: Use the `file_url` field to specify a URL to a PDF file.
   ```json
   "input": {
      "file_url": "https://example.com/invoice.pdf",
      ...
   }
   ```

2. **Local Files**: Use the `file` field to specify a path to a local PDF file.
   ```json
   "input": {
      "file": "/path/to/your/local/invoice.pdf",
      ...
   }
   ```

When using local files, the test will:
1. Read the file from the specified path
2. Upload it to the API using a multipart form request
3. Process the response in the same way as with remote files

> **Note:** Make sure the local file paths are accessible from the machine running the tests.

### Using Focus Mode

You can add a `focus` attribute to test cases to run only those specific tests:

```json
{
   "input": {
      "file": "/path/to/your/local/invoice.pdf",
      ...
   },
   "output": {
      ...
   },
   "focus": true
}
```

When running tests with the `--focus` flag (or using the `test:integration:focus` script), only test cases with `focus: true` will be executed. This is useful when:

1. Debugging specific test cases
2. Working on a specific feature
3. Testing with a large number of test cases but only wanting to run a few

To run only focused tests:

```bash
pnpm test:integration:focus
```

### Configuration Loading Priority

The test runner will load configuration files in the following order:

1. **Primary**: Load both `test-config-invoice.json` and `test-config-tax-invoice.json`
2. **Fallback**: If actual files don't exist, try corresponding `.example` files

### Configuration Fields

#### Environment Configuration (`test-env.json`)
- `base_url`: Base URL of the API
- `api_key`: API key for authentication
- `local_file_base_path`: Base path for local test files

#### Test Configuration
- `tests`: Array of test cases
  - `uuid`: Unique identifier for the test case
  - `input`: Input data for the API call
    - `file`: Path to local PDF file (relative to `local_file_base_path`)
    - `file_url`: URL of the PDF file to process (alternative to `file`)
    - `vendor_name`: Name of the vendor
    - `invoice_amount`: Invoice amount
    - `vat_amount`: VAT amount
    - `invoice_date`: Invoice date (for standard invoices)
    - `invoice_number`: Invoice number (for standard invoices)
    - `tax_invoice_date`: Tax invoice date (for tax invoices)
    - `tax_invoice_number`: Tax invoice number (for tax invoices)
  - `output`: Expected output with detailed field validation
    - `fields`: Object containing field-by-field validation with `input_value`, `ocr_value`, and `status`
    - `summary`: Summary statistics with `total_fields`, `matched`, `mismatched`, `not_found`
    - `is_colored`: Boolean indicating if the document is colored
    - `color_pages`: Array of page numbers that are colored
    - `total_pages`: Total number of pages in the document
  - `focus`: Boolean flag to run only this test when focus mode is enabled

## Running the Tests

To run the integration tests:

```bash
# Run all tests
pnpm test:integration

# Run with verbose output
pnpm test:integration -- --verbose

# Run only test cases with focus=true
pnpm test:integration:focus
```

## How It Works

1. The test loads configuration files in priority order:
   - First tries to load `test-config-invoice.json` and `test-config-tax-invoice.json`
   - Uses `.example` files if actual files don't exist
2. Loads environment configuration from `test-env.json`
3. For each test case:
   - Determines whether to call the process-invoice or process-tax-invoice API based on the input fields
   - Makes the API call with the provided input data
   - Validates the response against the expected output with detailed field-by-field comparison
   - Reports any mismatches with detailed information

## Adding New Test Cases

- Add invoice test cases to `test-config-invoice.json`
- Add tax invoice test cases to `test-config-tax-invoice.json`

## File Organization

```
tests/integration/
├── README.md
├── test-config-invoice.json          # Invoice test cases
├── test-config-invoice.json.example  # Invoice test examples
├── test-config-tax-invoice.json      # Tax invoice test cases
├── test-config-tax-invoice.json.example # Tax invoice test examples
├── test-env.json                     # Environment configuration
├── test-env.json.example             # Environment examples
└── remote-api-test.ts                # Test runner
```

## Security Note

Be careful with the API key in the configuration file. Do not commit real API keys to version control. Consider using environment variables or a local configuration file that is excluded from version control.
