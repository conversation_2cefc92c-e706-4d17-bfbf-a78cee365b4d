import { describe, it, expect } from 'vitest';
import { matchField } from '@/lib/api/field-matching';

describe('Field Matching', () => {
  describe('vendor_name matching', () => {
    it('should match PT variations', () => {
      const testCases = [
        {
          input: 'PT WAJA KAMAJAYA SENTOSA',
          ocr: 'P.T. WAJA KAMAJAYA SENTOSA',
          description: 'PT vs P.T.'
        },
        {
          input: 'PT. WAJA KAMAJAYA SENTOSA',
          ocr: 'P.T. WAJA KAMAJAYA SENTOSA',
          description: 'PT. vs P.T.'
        },
        {
          input: 'P T WAJA KAMAJAYA SENTOSA',
          ocr: 'P.T. WAJA KAMAJAYA SENTOSA',
          description: 'P T vs P.T.'
        },
        {
          input: 'PT AIR LIQUIDE INDONESIA',
          ocr: 'PT. AIR LIQUIDE INDONESIA',
          description: 'PT vs PT.'
        }
      ];

      testCases.forEach(({ input, ocr, description }) => {
        const result = matchField({
          input_value: input,
          ocr: { ocr_value: ocr },
          field: 'vendor_name',
          type: 'string'
        });

        expect(result.status).toBe('matched', `Failed for ${description}: ${input} vs ${ocr}`);
      });
    });

    it('should match CV variations', () => {
      const testCases = [
        {
          input: 'CV EXAMPLE COMPANY',
          ocr: 'C.V. EXAMPLE COMPANY',
          description: 'CV vs C.V.'
        },
        {
          input: 'CV. EXAMPLE COMPANY',
          ocr: 'C V EXAMPLE COMPANY',
          description: 'CV. vs C V'
        }
      ];

      testCases.forEach(({ input, ocr, description }) => {
        const result = matchField({
          input_value: input,
          ocr: { ocr_value: ocr },
          field: 'vendor_name',
          type: 'string'
        });

        expect(result.status).toBe('matched', `Failed for ${description}: ${input} vs ${ocr}`);
      });
    });

    it('should match TBK variations', () => {
      const result = matchField({
        input_value: 'TBK EXAMPLE COMPANY',
        ocr: { ocr_value: 'T.B.K. EXAMPLE COMPANY' },
        field: 'vendor_name',
        type: 'string'
      });

      expect(result.status).toBe('matched');
    });

    it('should not match different company names', () => {
      const result = matchField({
        input_value: 'PT COMPANY A',
        ocr: { ocr_value: 'PT COMPANY B' },
        field: 'vendor_name',
        type: 'string'
      });

      expect(result.status).toBe('mismatched');
    });

    it('should handle case insensitive matching', () => {
      const result = matchField({
        input_value: 'pt waja kamajaya sentosa',
        ocr: { ocr_value: 'P.T. WAJA KAMAJAYA SENTOSA' },
        field: 'vendor_name',
        type: 'string'
      });

      expect(result.status).toBe('matched');
    });
  });

  describe('other field types', () => {
    it('should match exact strings for non-vendor fields', () => {
      const result = matchField({
        input_value: 'INV-123',
        ocr: { ocr_value: 'INV-123' },
        field: 'invoice_number',
        type: 'string'
      });

      expect(result.status).toBe('matched');
    });

    it('should match numbers correctly', () => {
      const result = matchField({
        input_value: 1000000,
        ocr: { ocr_value: '1,000,000.00' },
        field: 'invoice_amount',
        type: 'number'
      });

      expect(result.status).toBe('matched');
    });
  });
});
