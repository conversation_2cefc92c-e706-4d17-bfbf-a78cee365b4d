import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { ProcessingTimeChart } from '@/components/admin/processing-time-chart';
import { FormattedLogEntry } from '@/lib/types/log';

// Mock recharts to avoid canvas issues in tests
vi.mock('recharts', () => ({
  LineChart: ({ children }: any) => <div data-testid="line-chart">{children}</div>,
  Line: () => <div data-testid="line" />,
  XAxis: () => <div data-testid="x-axis" />,
  YAxis: () => <div data-testid="y-axis" />,
  CartesianGrid: () => <div data-testid="cartesian-grid" />,
  Tooltip: () => <div data-testid="tooltip" />,
  Legend: () => <div data-testid="legend" />,
  ResponsiveContainer: ({ children }: any) => <div data-testid="responsive-container">{children}</div>,
}));

describe('ProcessingTimeChart', () => {
  const mockDateRange = {
    from: new Date('2024-01-01'),
    to: new Date('2024-01-31'),
  };

  const mockData: FormattedLogEntry[] = [
    {
      id: '1',
      requestType: 'invoice',
      requestTimestamp: '2024-01-15T10:00:00Z',
      processedAt: '2024-01-15T10:00:05Z', // 5 seconds processing time
      fileName: 'test1.pdf',
      fileSize: 1024,
      processingStatus: 'success',
      errorMessage: null,
      processingId: 'proc-1',
      inputVendorName: 'Test Vendor',
      ocrVendorName: 'Test Vendor',
      inputDocumentNo: 'INV-001',
      ocrDocumentNo: 'INV-001',
      inputDocumentDate: '2024-01-15',
      ocrDocumentDate: '2024-01-15',
      inputInvoiceAmount: 100.0,
      ocrInvoiceAmount: 100.0,
      inputVatAmount: 10.0,
      ocrVatAmount: 10.0,
      isColored: false,
      colorPages: [],
      totalPages: 1,
      matchingResult: null,
      totalFields: 5,
      matchedFields: 5,
      mismatchedFields: 0,
      notFoundFields: 0,
      apiKeyId: null,
    },
    {
      id: '2',
      requestType: 'taxInvoice',
      requestTimestamp: '2024-01-16T14:00:00Z',
      processedAt: '2024-01-16T14:00:03Z', // 3 seconds processing time
      fileName: 'test2.pdf',
      fileSize: 2048,
      processingStatus: 'success',
      errorMessage: null,
      processingId: 'proc-2',
      inputVendorName: 'Another Vendor',
      ocrVendorName: 'Another Vendor',
      inputDocumentNo: 'TAX-001',
      ocrDocumentNo: 'TAX-001',
      inputDocumentDate: '2024-01-16',
      ocrDocumentDate: '2024-01-16',
      inputInvoiceAmount: 200.0,
      ocrInvoiceAmount: 200.0,
      inputVatAmount: 20.0,
      ocrVatAmount: 20.0,
      isColored: false,
      colorPages: [],
      totalPages: 2,
      matchingResult: null,
      totalFields: 5,
      matchedFields: 4,
      mismatchedFields: 1,
      notFoundFields: 0,
      apiKeyId: null,
    },
  ];

  it('renders the chart components', () => {
    render(<ProcessingTimeChart data={mockData} dateRange={mockDateRange} />);

    // Check that the main chart components are rendered
    expect(screen.getByText('Overall Processing Time')).toBeInTheDocument();
    expect(screen.getByText('Processing Time by Document Type')).toBeInTheDocument();
    
    // Check that chart elements are present
    expect(screen.getAllByTestId('line-chart')).toHaveLength(2);
    expect(screen.getAllByTestId('responsive-container')).toHaveLength(2);
  });

  it('renders with empty data', () => {
    render(<ProcessingTimeChart data={[]} dateRange={mockDateRange} />);

    // Should still render the chart structure even with no data
    expect(screen.getByText('Overall Processing Time')).toBeInTheDocument();
    expect(screen.getByText('Processing Time by Document Type')).toBeInTheDocument();
  });

  it('filters data by date range', () => {
    const dataOutsideRange: FormattedLogEntry[] = [
      {
        ...mockData[0],
        id: '3',
        requestTimestamp: '2023-12-01T10:00:00Z', // Outside date range
        processedAt: '2023-12-01T10:00:02Z',
      },
    ];

    render(<ProcessingTimeChart data={[...mockData, ...dataOutsideRange]} dateRange={mockDateRange} />);

    // Chart should still render (filtering is handled internally)
    expect(screen.getByText('Overall Processing Time')).toBeInTheDocument();
  });

  it('handles logs without processedAt timestamp', () => {
    const incompleteData: FormattedLogEntry[] = [
      {
        ...mockData[0],
        id: '4',
        processedAt: null, // No processed timestamp
      },
    ];

    render(<ProcessingTimeChart data={incompleteData} dateRange={mockDateRange} />);

    // Should render without errors
    expect(screen.getByText('Overall Processing Time')).toBeInTheDocument();
  });
});
