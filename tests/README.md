# Testing Documentation

This directory contains tests for the Invoice Matching application. The tests are written using Vitest and React Testing Library.

## Test Structure

The tests are organized to mirror the application structure:

- `tests/app/api/` - Tests for API routes
- `tests/lib/` - Tests for utility functions and services
- `tests/mocks/` - Mock implementations for external dependencies
- `tests/integration/` - Integration tests for remote API endpoints
- `tests/utils.ts` - Utility functions for testing

## Running Tests

To run the tests, use the following commands:

```bash
# Run tests in watch mode
pnpm test

# Run tests once
pnpm test:run

# Run tests with coverage report
pnpm test:coverage

# Run integration tests against remote API
pnpm test:integration
```

## Mocking Strategy

The tests use several mocking strategies:

1. **MSW (Mock Service Worker)** - Used to mock HTTP requests to external APIs
2. **Prisma Mock** - Used to mock database operations
3. **Function Mocks** - Used to mock specific functions like color detection

## Test Utilities

The `tests/utils.ts` file contains utility functions for creating test data:

- `createMockPdfBuffer()` - Creates a mock PDF buffer
- `createMockFormData()` - Creates a mock FormData object with a PDF file
- `createMockInvoiceFormData()` - Creates a mock FormData for invoice processing
- `createMockTaxInvoiceFormData()` - Creates a mock FormData for tax invoice processing
- `MockNextRequest` - A mock implementation of NextRequest for testing API routes

## Adding New Tests

When adding new tests:

1. Follow the existing directory structure
2. Import necessary mocks from the `tests/mocks` directory
3. Use the utility functions from `tests/utils.ts` where appropriate
4. Make sure to reset mocks in `beforeEach` blocks

## Integration Tests

The `tests/integration` directory contains tests that call the remote API endpoints for invoice and tax invoice processing. These tests are configured using a JSON file that specifies the environment settings and test cases.

To run the integration tests:

```bash
pnpm test:integration
```

See [integration/README.md](integration/README.md) for more details on the integration tests.

## Test Coverage

The test coverage report can be generated using:

```bash
pnpm test:coverage
```

This will generate a coverage report in the `coverage` directory.
