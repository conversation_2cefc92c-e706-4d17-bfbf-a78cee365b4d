# Invoice Matching System

A Next.js application for processing and matching invoices and tax invoices against provided data using an external Document Processing Service (DPS).

## Features

- API endpoints for processing standard invoices and tax invoices
- PDF data extraction and matching via external DPS API integration
- Colored invoice detection for standard invoices
- Structured JSON responses with detailed matching results
- PostgreSQL logging of all processing activities with input/OCR field pairs
- Admin dashboard for viewing and filtering logs
- Secure authentication for admin dashboard using NextAuth.js

## Tech Stack

- Next.js 15 with App Router
- TypeScript
- Prisma ORM with PostgreSQL
- Tailwind CSS with shadcn/ui components
- API key authentication for endpoints
- NextAuth.js authentication for admin dashboard
- Vitest for testing

## Database Conventions

- Tables and columns use `snake_case` naming convention in the database
- The application code uses `camelCase` for field names (Prisma handles the mapping)
- The main table is `processing_logs` which stores all invoice processing activities
- Each field has both input and OCR versions (e.g., `inputVendorName` and `ocrVendorName`)
- Processing timestamps are tracked with `requestTimestamp` (request time) and `processedAt` (DPS response time)

## Getting Started

### Prerequisites

- Node.js 18+ and pnpm
- PostgreSQL database
- ImageMagick (for PDF color detection)

### Installation

1. Clone the repository
2. Install dependencies:

```bash
pnpm install
```

3. Create an `.env` file with your database connection string and API keys:

```
# Database connection string
DATABASE_URL="postgresql://username:password@localhost:5432/db_name"

# NextAuth Configuration
NEXTAUTH_SECRET="your-nextauth-superlong-secret-key"
NEXTAUTH_URL="http://localhost:3000"

# Document Processing Service
DONA_HOST="https://your-document-processing-service.com"
DONA_API_KEY="your-document-processing-api-key"
```

4. Initialize the database:

```bash
# Generate Prisma client
pnpm db:generate

# Push schema to database
pnpm db:push

# Or do both in one command
pnpm db:setup
```

5. Create an admin user:

```bash
pnpm create-admin
```

This will prompt you to enter an email, name, and password for the admin user.

### Development

Run the development server:

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) in your browser.

### Testing

Run the tests:

```bash
# Run tests in watch mode
pnpm test

# Run tests once
pnpm test:run

# Run tests with verbose output
pnpm test:coverage

# Run integration tests against remote API
pnpm test:integration
```

See [TESTING.md](TESTING.md) for more details on the testing approach.

#### Integration Tests

The project includes integration tests that call the remote API endpoints for invoice and tax invoice processing. These tests are configured using a JSON file that specifies the environment settings and test cases.

See [tests/integration/README.md](tests/integration/README.md) for more details on the integration tests.

### Build for Production

```bash
pnpm build
pnpm start
```

## PDF Color Detection

The system includes enhanced PDF color detection to identify if invoices contain color and which specific pages have color. This is useful for determining if an invoice is a colored document, which may affect processing rules.

### How It Works

The color detection uses ImageMagick to analyze PDFs at the pixel level:

1. Converts PDF pages to images
2. Analyzes RGB values of pixels to detect non-grayscale colors
3. Provides detailed results including which pages contain color
4. Handles edge cases like pages with minimal content or large white spaces

### Testing Color Detection

A standalone script is provided to test the color detection on PDF files:

```bash
# Test a single PDF file
node scripts/test-enhanced-color.js path/to/invoice.pdf

# Test all PDFs in a directory
node scripts/test-enhanced-color.js path/to/pdf/folder
```

The script will output detailed results and save them to `enhanced-color-detection-results.json`.

### Requirements

- ImageMagick must be installed on the system
- Works with both ImageMagick v6 and v7 (automatically detects version)

For more details on the color detection implementation, see [PDF-COLOR-DETECTION.md](PDF-COLOR-DETECTION.md).

## Authentication

The application uses NextAuth.js for authentication in the admin dashboard. The authentication flow is as follows:

1. Users navigate to `/admin/login`
2. After successful authentication, users are redirected to the admin dashboard.
3. Users can log out by visiting `/admin/logout`.

To create a new admin user, use the `pnpm create-admin` command as described in the installation section.

## API Endpoints

All API endpoints require authentication using an API key. The API key should be included in the `X-API-Key` header of the request. API keys can be created and managed in the admin dashboard under the "API Documentation" tab.

```bash
curl -X POST "http://localhost:3000/api/process-invoice" \
  -H "X-API-Key: ki_12345678.your-api-key-secret" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/invoice.pdf"
```

### Process Standard Invoice

`POST /api/process-invoice`

Processes a standard invoice PDF and returns matching results.

**Request Body (multipart/form-data):**
- `file`: PDF file (required)
- `vendor_name`: Vendor name to match against
- `invoice_number`: Invoice number to match against
- `invoice_date`: Invoice date to match against (supports various formats)
- `invoice_amount`: Invoice amount to match against
- `vat_amount`: VAT amount to match against

**Example Request:**
```bash
curl -X POST "http://localhost:3000/api/process-invoice" \
  -H "X-API-Key: ki_12345678.your-api-key-secret" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/invoice.pdf" \
  -F "vendor_name=PT. Parker Hanfin Indonesia" \
  -F "invoice_amount=7674695" \
  -F "vat_amount=760555" \
  -F "invoice_date=31/10/24" \
  -F "invoice_number=69657555"
```

**Response:**
```json
{
   "document_type":"standard_invoice",
   "page_count":1,
   "results":{
      "summary":{
         "total_fields":5,
         "matched":4,
         "mismatched":1,
         "not_found":0
      },
      "fields":{
         "vendor_name":{
            "input_value":"PT. Parker Hannifin Indonesia",
            "ocr_value":"PT. SHIBAURA SHEARING INDONESIA",
            "status":"mismatched"
         },
         "invoice_amount":{
            "input_value":"7674695.0",
            "ocr_value":"6233760",
            "status":"mismatched"
         },
         "vat_amount":{
            "input_value":"760555.0",
            "ocr_value":"617760",
            "status":"mismatched"
         },
         "invoice_number":{
            "input_value":"69657555",
            "ocr_value":"SI-24-11-0434",
            "status":"mismatched"
         },
         "invoice_date":{
            "input_value":"31/10/24",
            "ocr_value":"2024-11-08",
            "status":"mismatched"
         }
      },
      "isColored": false
   },
   "gs_uri":"gs://ki-files/documents/ssi_invoice_7f5c007d.pdf",
   "document_processing_log_id":"5557f653-693b-4a91-90a1-f426c7f15c91"
}
```

### Process Tax Invoice

`POST /api/process-tax-invoice`

Processes a tax invoice PDF and returns matching results.

**Request Body (multipart/form-data):**
- `file`: PDF file (required)
- `vendor_name`: Vendor name to match against
- `tax_invoice_number`: Tax invoice number to match against
- `tax_invoice_date`: Tax invoice date to match against (supports various formats)
- `invoice_amount`: Invoice amount to match against
- `vat_amount`: VAT amount to match against

**Example Request:**
```bash
curl -X POST "http://localhost:3000/api/process-tax-invoice" \
  -H "X-API-Key: ki_12345678.your-api-key-secret" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/tax-invoice.pdf" \
  -F "vendor_name=PT. SHIBAURA SHEARING" \
  -F "invoice_amount=5616000" \
  -F "vat_amount=617760" \
  -F "tax_invoice_date=8-11-2024" \
  -F "tax_invoice_number=010.009-24.70808479"
```

**Response:**
```json
{
   "document_processing_log_id" : "e0f56cb7-e161-4814-bde6-673548837334",
   "document_type" : "tax_invoice",
   "gs_uri" : "gs://ki-files/documents/ssi_faktur_pajak_a71a9031.pdf",
   "page_count" : 1,
   "results" : {
      "fields" : {
         "invoice_amount" : {
            "input_value" : "5616000.0",
            "ocr_value" : "5616000,00",
            "status" : "matched"
         },
         "tax_invoice_date" : {
            "input_value" : "2024-11-08",
            "ocr_value" : "2024-11-08",
            "status" : "matched"
         },
         "tax_invoice_number" : {
            "input_value" : "010.009-24.70808479",
            "ocr_value" : "010.009-24.70808479",
            "status" : "matched"
         },
         "vat_amount" : {
            "input_value" : "617760.0",
            "ocr_value" : "617.760,00",
            "status" : "matched"
         },
         "vendor_name" : {
            "input_value" : "PT. SHIBAURA SHEARING",
            "ocr_value" : "PT SHIBAURA SHEARING INDONESIA",
            "status" : "matched"
         }
      },
      "summary" : {
         "matched" : 4,
         "mismatched" : 1,
         "not_found" : 0,
         "total_fields" : 5
      }
   }
}
```

## Database Schema

The main table is `processing_logs` with the following structure:

```sql
CREATE TABLE processing_logs (
  id UUID PRIMARY KEY,
  request_type VARCHAR NOT NULL,
  request_timestamp TIMESTAMP NOT NULL DEFAULT NOW(),
  file_name VARCHAR,
  file_size INTEGER,
  processing_status VARCHAR NOT NULL,
  error_message VARCHAR,
  processing_id VARCHAR,

  -- Input fields (from user)
  input_vendor_name VARCHAR,
  input_document_no VARCHAR,
  input_document_date TIMESTAMP,
  input_invoice_amount DECIMAL,
  input_vat_amount DECIMAL,

  -- OCR fields (from DPS)
  ocr_vendor_name VARCHAR,
  ocr_document_no VARCHAR,
  ocr_document_date TIMESTAMP,
  ocr_invoice_amount DECIMAL,
  ocr_vat_amount DECIMAL,

  -- Additional fields
  is_colored BOOLEAN,
  matching_result JSONB,
  total_fields INTEGER,
  matched_fields INTEGER,
  mismatched_fields INTEGER,
  not_found_fields INTEGER,

  -- Timestamps
  processed_at TIMESTAMP
);
```

## Admin Dashboard

The admin dashboard is available at `/admin` and provides:

- Overview of processing statistics
- Detailed logs of all invoice processing
- Filtering by type, status, date, vendor, and invoice number
- Detailed view of each processing log
- API key management

Access requires authentication with the credentials created using the `pnpm create-admin` command.

### API Key Management

The admin dashboard includes an "API Documentation" tab that allows you to:

1. Create new API keys with a name and optional description
2. View existing API keys
3. Activate/deactivate API keys
4. Delete API keys

API keys are securely stored as hashes in the database, following security best practices. The full API key is only shown once at creation time and cannot be recovered if lost (only replaced).

## Project Structure

- `/app` - Next.js app router pages and API routes
- `/components` - React components
  - `/ui` - UI components from shadcn/ui
  - `/admin` - Admin dashboard components
  - `/forms` - Form components
- `/lib` - Utility functions and shared code
  - `/api` - API client functions
  - `/db` - Database connection and utilities
  - `/types` - TypeScript type definitions
- `/prisma` - Prisma schema and migrations
- `/scripts` - Utility scripts
  - `test-enhanced-color.js` - Script to test PDF color detection
- `/tests` - Test files and utilities
  - `/app` - Tests for API routes
  - `/lib` - Tests for utility functions
  - `/mocks` - Mock implementations for testing

## License

[MIT](LICENSE)
