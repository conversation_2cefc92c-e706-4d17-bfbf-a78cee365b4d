# syntax=docker/dockerfile:1

# ===== STAGE: Setup =====
# This Dockerfile is intended for building an image containing all necessary tools
# and application code to perform setup tasks like DB migrations, data seeding,
# or other one-off scripts.
# It does not run any default command; you must specify the command at runtime.

FROM node:22-alpine AS setup
WORKDIR /app

# Set environment variables
# NODE_ENV=development ensures that any tools (like Prisma CLI if in devDependencies)
# are available and behave as expected in a tooling context.
ENV NODE_ENV=development
ENV NEXT_TELEMETRY_DISABLED=1
# Copied from your original builder stage; adjust if not needed for setup tasks.
ENV NODE_OPTIONS="--max-old-space-size=4096"

# Install pnpm (using the same version as in your original Dockerfile)
# This is a global installation.
RUN npm install -g pnpm@10.8.1

# Copy package manager files.
# This layer is cached if these files don't change, speeding up subsequent builds.
COPY package.json pnpm-lock.yaml ./

# Install all dependencies (including devDependencies like Prisma CLI).
# Uses --frozen-lockfile to ensure reproducible dependency installation.
# This layer is cached if package.json or pnpm-lock.yaml haven't changed.
RUN pnpm install --frozen-lockfile

# Copy Prisma schema.
# This is done separately to leverage caching if only other application code changes.
COPY prisma ./prisma

# Generate Prisma client.
# This step is taken from your original builder stage (pnpm db:generate).
# It's good practice as your scripts will likely need an up-to-date client.
# Assumes 'db:generate' is a script in your package.json (e.g., "prisma generate").
RUN pnpm db:generate

# Copy the rest of the application code into the image.
# This includes your 'scripts/' directory and any other necessary files.
# This layer will be rebuilt if any file in the build context changes (respecting .dockerignore).
COPY . .

# Set the entrypoint to "sh -c".
# This means that the command string you provide to `docker run`
# will be executed directly by the shell.
ENTRYPOINT ["sh", "-c"]

# No default CMD is specified.
# You MUST provide a command string when running a container from this image.
#
# IMPORTANT:
# This image expects necessary environment variables (e.g., DATABASE_URL)
# to be provided at runtime via `docker run -e ...`.
#
# EXAMPLES of how to run commands:
#
# 1. Run Prisma migrations:
#    docker run --rm -e DATABASE_URL="your_db_url" your-setup-image-name "pnpm prisma migrate deploy"
#
# 2. Create an admin user:
#    docker run --rm -e DATABASE_URL="your_db_url" your-setup-image-name "node scripts/create-admin.js"
#
# 3. Run a different pnpm script (e.g., reset password):
#    docker run --rm -e DATABASE_URL="your_db_url" your-setup-image-name "pnpm reset-admin-password"
#
# 4. Run multiple commands sequentially:
#    docker run --rm -e DATABASE_URL="your_db_url" your-setup-image-name "pnpm prisma migrate deploy && node scripts/create-admin.js"
#
# 5. For an interactive shell inside the container (for debugging):
#    docker run --rm -it -e DATABASE_URL="your_db_url" your-setup-image-name "sh"
#
# DATABASE_URL="postgresql://fajar:@localhost:5432/invoice_matching_docker"

# docker run --rm \
#   -e DATABASE_URL="postgresql://kimpgadmin:<EMAIL>:5432/invoice_matching_docker_test" \
#   invoice-matching:setup \
#   "pnpm prisma migrate deploy"

# docker run -it --rm \
#   -e DATABASE_URL="postgresql://kimpgadmin:<EMAIL>:5432/invoice_matching_docker_test" \
#   invoice-matching:setup \
#   "pnpm create-admin"

