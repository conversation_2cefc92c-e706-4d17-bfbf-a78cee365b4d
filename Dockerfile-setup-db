# ===== STAGE 1: Base for Setup =====
# Sets up Node.js and pnpm.
FROM node:22-alpine AS setup_base
WORKDIR /app

# Install pnpm
RUN npm install -g pnpm@10.8.1 \
    && pnpm --version
ENV PATH="/usr/local/bin:$PATH"

# Copy package files.
COPY package.json pnpm-lock.yaml ./

# ===== STAGE 2: Dependencies for Setup =====
# Installs all dependencies needed for prisma and create-admin.
FROM setup_base AS setup_deps

# Install all dependencies based on the lockfile.
RUN pnpm install --frozen-lockfile

# ===== STAGE 3: Setup DB and Admin =====
FROM setup_deps AS setup_db_admin
WORKDIR /app

# Copy Prisma schema and scripts
COPY prisma ./prisma
COPY scripts ./scripts

# Set environment variables if needed for Prisma or create-admin
ENV NODE_ENV=production
ENV NEXT_TELEMETRY_DISABLED=1

# Execute Prisma migrations
RUN pnpm prisma migrate deploy


# This image is for setup, so it can exit after commands are run.
# No CMD needed as it's expected to be run with `docker run` and then exit.