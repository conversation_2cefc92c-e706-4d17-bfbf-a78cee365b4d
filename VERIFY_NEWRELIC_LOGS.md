# Verifying New Relic Log Forwarding

This guide helps you verify that logs are being properly forwarded to New Relic.

## Prerequisites

1. **Valid New Relic License Key**: Make sure you have a real license key (not the dummy one)
2. **New Relic Account Access**: Ensure you can access your New Relic dashboard
3. **Application Running**: Your application should be running with the New Relic agent

## Step 1: Set Your Real License Key

Update your environment variables with your actual New Relic license key:

```bash
# In .env or your deployment environment
NEW_RELIC_LICENSE_KEY="your_actual_license_key_here"
NEW_RELIC_APP_NAME="Invoice Matching API - Production"
```

## Step 2: Run Diagnostic Scripts

We've created diagnostic scripts to test New Relic integration:

### Check New Relic Configuration
```bash
pnpm newrelic:check
```

This script will:
- ✅ Verify environment variables are set
- ✅ Test New Relic agent loading
- ✅ Check agent configuration
- ✅ Test custom attributes, events, and metrics

### Test Log Forwarding Specifically
```bash
pnpm newrelic:test-logs
```

This script will:
- 📝 Generate various types of log entries
- 🧪 Test different log formats (JSON, structured, errors)
- 🚀 Create high-volume logs
- 🎯 Add custom attributes for correlation

## Step 3: Check New Relic Dashboard

### 3.1 Application Performance Monitoring (APM)
1. Go to **APM & Services** in New Relic
2. Look for your application: "Invoice Matching API - Production"
3. Verify you see:
   - Response times
   - Throughput
   - Error rates
   - Database queries

### 3.2 Logs Section
1. Go to **Logs** in New Relic
2. Look for logs from your application
3. Search for test logs: `message:"TEST LOG"`
4. Verify you see:
   - Different log levels (INFO, WARN, ERROR)
   - JSON structure preserved
   - Custom attributes attached
   - Timestamps are correct

### 3.3 Events Section
1. Go to **Events** in New Relic
2. Look for custom events:
   - `DiagnosticTest`
   - `LogForwardingTest`
   - `InvoiceProcessingStarted`
   - `InvoiceProcessingCompleted`

### 3.4 Metrics Section
1. Go to **Metrics** in New Relic
2. Look for custom metrics:
   - `Custom/DiagnosticTest`
   - `Custom/PDF/TotalPages`
   - `Custom/External/DPS/ResponseTime`

## Step 4: Troubleshooting

### Issue: No Data Appearing in New Relic

**Check 1: License Key**
```bash
# Verify license key is set correctly
echo $NEW_RELIC_LICENSE_KEY
```

**Check 2: Agent Initialization**
Look for New Relic initialization messages in your application logs:
```bash
# In Docker
docker-compose logs | grep -i newrelic

# In development
pnpm dev | grep -i newrelic
```

**Check 3: Network Connectivity**
Ensure your application can reach New Relic servers:
```bash
# Test connectivity
curl -I https://collector.newrelic.com
```

### Issue: Logs Not Appearing

**Check 1: Log Forwarding Configuration**
Verify in `newrelic.js`:
```javascript
application_logging: {
  enabled: true,
  forwarding: {
    enabled: true,
    max_samples_stored: 10000
  }
}
```

**Check 2: Log Format**
New Relic works best with structured JSON logs. Verify your logs are in JSON format:
```bash
# Check log output format
docker-compose logs | head -10
```

**Check 3: Log Level**
Ensure log level allows your messages:
```bash
# Check current log level
echo $NEW_RELIC_LOG_LEVEL
```

### Issue: Partial Data Missing

**Custom Events Not Appearing:**
- Check if you're calling `newrelic.recordCustomEvent()`
- Verify event names don't contain special characters
- Ensure attributes are valid JSON types

**Custom Metrics Missing:**
- Check if you're calling `newrelic.recordMetric()`
- Verify metric names follow New Relic conventions
- Ensure values are numbers

**Errors Not Appearing:**
- Check if you're calling `newrelic.noticeError()`
- Verify errors are actual Error objects
- Check error collector configuration

## Step 5: API Endpoint Testing

### New Relic Test Endpoint
We've created a dedicated test endpoint that exercises all New Relic features:

```bash
# Test all New Relic functionality
curl http://localhost:3000/api/newrelic-test

# Test with POST data
curl -X POST http://localhost:3000/api/newrelic-test \
  -H "Content-Type: application/json" \
  -d '{"test": "data", "timestamp": "2024-01-01"}'
```

This endpoint will:
- ✅ Set custom transaction names
- ✅ Add custom attributes
- ✅ Record custom events
- ✅ Record custom metrics
- ✅ Test error recording
- ✅ Generate various log types
- ✅ Test structured logging

### Real Traffic Test
1. Make actual API calls to your endpoints:
```bash
# Test invoice processing
curl -X POST http://localhost:3000/api/process-invoice \
  -H "Content-Type: application/json" \
  -H "X-API-Key: your_api_key" \
  -d '{
    "file_url": "https://example.com/invoice.pdf",
    "vendor_name": "Test Vendor",
    "invoice_number": "INV-001",
    "invoice_date": "2024-01-01",
    "invoice_amount": 100.00,
    "vat_amount": 10.00
  }'
```

2. Check New Relic for:
   - Transaction traces for `/api/process-invoice`
   - Custom events for invoice processing
   - Error traces if any failures occur
   - Log entries for the request

### Performance Monitoring
1. Monitor response times
2. Check for slow database queries
3. Verify external API call monitoring
4. Review error rates and patterns

## Step 6: Docker-Specific Verification

### Check Container Logs
```bash
# View all logs
docker-compose logs -f

# Filter for New Relic messages
docker-compose logs | grep -i "newrelic\|agent\|collector"
```

### Verify Environment Variables in Container
```bash
# Check environment variables inside container
docker-compose exec invoice-matching env | grep NEW_RELIC
```

### Test Log Output Format
```bash
# Verify logs are going to stdout (required for New Relic)
docker-compose logs --no-color | head -20
```

## Expected Timeline

- **APM Data**: Appears within 1-2 minutes
- **Custom Events**: Appear within 1-2 minutes
- **Logs**: Appear within 1-5 minutes
- **Metrics**: Appear within 1-2 minutes
- **Errors**: Appear immediately

## Success Indicators

✅ **New Relic Dashboard shows your application**
✅ **Transaction traces appear for API endpoints**
✅ **Custom events are recorded**
✅ **Logs appear in New Relic Logs section**
✅ **Errors are captured and reported**
✅ **Custom metrics are recorded**
✅ **Database queries are monitored**

## Getting Help

If you're still not seeing logs after following this guide:

1. **Check New Relic Status**: Visit [status.newrelic.com](https://status.newrelic.com)
2. **Review Documentation**: [New Relic Node.js Agent Docs](https://docs.newrelic.com/docs/apm/agents/nodejs-agent)
3. **Enable Debug Logging**: Set `NEW_RELIC_LOG_LEVEL=trace` for detailed debugging
4. **Contact Support**: Use New Relic support if you have a paid account

## Quick Debug Commands

```bash
# Check if New Relic is working
pnpm newrelic:check

# Test log forwarding
pnpm newrelic:test-logs

# Test New Relic integration via API
curl http://localhost:3000/api/newrelic-test

# View application logs
docker-compose logs -f

# Check environment variables
env | grep NEW_RELIC

# Test API endpoint
curl http://localhost:3000/api/health
```
