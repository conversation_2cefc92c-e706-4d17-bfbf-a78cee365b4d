import * as Sentry from '@sentry/nextjs';
import {
  ExtractedInvoiceData,
  ExtractedTaxInvoiceData,
  ExternalInvoiceResponse,
  ExternalTaxInvoiceResponse,
  InvoiceData,
  TaxInvoiceData
} from '../types/invoice';

// Get environment variables - support both DPS and DONA naming
const DONA_HOST = process.env.DONA_HOST || 'http://localhost:8000';
const DONA_API_KEY = process.env.DONA_API_KEY || 'test-api-key';

// Log warning if environment variables are not set in non-test environments
if (!process.env.DONA_HOST && process.env.NODE_ENV !== 'test') {
  console.error('DONA_HOST environment variable is not set');
}
if (!process.env.DONA_API_KEY && process.env.NODE_ENV !== 'test') {
  console.error('DONA_API_KEY environment variable is not set');
}

/**
 * Process and match a standard invoice PDF file
 * @param pdfBuffer The PDF file as a buffer
 * @param inputData Optional input data for matching
 * @returns External service response
 */
export async function processInvoiceFile(
  pdfBuffer: Buffer,
  inputData?: InvoiceData
): Promise<ExternalInvoiceResponse> {
  return await Sentry.startSpan(
    {
      op: 'http.client',
      name: 'DONA API - Process Invoice File',
      attributes: {
        'external.service': 'DONA',
        'external.endpoint': '/documents/invoice-file',
        'external.method': 'POST',
        'file.size_bytes': pdfBuffer.length,
        'input.vendor_name': inputData?.vendorName || '',
        'input.invoice_number': inputData?.invoiceNo || '',
        'input.has_amount': !!inputData?.invoiceAmount,
        'input.has_vat': !!inputData?.vatAmount,
      },
    },
    async (span) => {
      try {
        // Create a FormData object to send the file and input data
        const formData = new FormData();

        // Add the PDF file to the form data
        const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
        formData.append('file', blob, 'invoice.pdf');

        // Always add all required fields, even if empty
        formData.append('vendor_name', inputData?.vendorName || '');
        formData.append('invoice_number', inputData?.invoiceNo || '');

        // Format date if provided, otherwise empty string
        const dateStr = inputData?.invoiceDate
          ? (typeof inputData.invoiceDate === 'string'
              ? inputData.invoiceDate
              : inputData.invoiceDate.toISOString().split('T')[0])
          : '';
        formData.append('invoice_date', dateStr);

        // Add numeric fields
        formData.append('invoice_amount', inputData?.invoiceAmount?.toString() || '');
        formData.append('vat_amount', inputData?.vatAmount?.toString() || '');

        // Add required boolean fields
        formData.append('show_confidence', 'true');
        formData.append('debug_timing', 'false');

        span.setAttribute('request.date_str', dateStr);
        span.setAttribute('request.form_fields_count', 7);

        // Make the API request
        const startTime = Date.now();
        const response = await fetch(`${DONA_HOST}/documents/invoice-file`, {
          method: 'POST',
          headers: {
            'X-API-Key': DONA_API_KEY,
          },
          body: formData,
        });
        const requestDuration = Date.now() - startTime;

        span.setAttribute('http.status_code', response.status);
        span.setAttribute('http.response_time_ms', requestDuration);

        if (!response.ok) {
          const errorText = await response.text();
          span.setStatus({ code: 2, message: `HTTP ${response.status}` });
          span.setAttribute('error.response_body', errorText);

          const error = new Error(`API request failed with status ${response.status}: ${errorText}`);
          Sentry.captureException(error, {
            tags: {
              operation: 'dona_api_call',
              endpoint: 'invoice-file',
            },
            extra: {
              status_code: response.status,
              response_body: errorText,
              file_size: pdfBuffer.length,
              input_data: inputData,
            },
          });
          throw error;
        }

        // Parse the response
        const data = await response.json();

        span.setStatus({ code: 1, message: 'Success' });
        span.setAttribute('response.processing_id', data.processing_id || 'unknown');
        span.setAttribute('response.has_results', !!data.results);

        if (data.results?.fields) {
          const fieldCount = Object.keys(data.results.fields).length;
          span.setAttribute('response.fields_count', fieldCount);
        }

        return data as ExternalInvoiceResponse;
      } catch (error) {
        span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
        span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');

        if (process.env.NODE_ENV !== 'test') {
          console.error('Error calling document processing service:', error);
        }

        // Re-capture if not already captured above
        if (!(error instanceof Error && error.message.includes('API request failed'))) {
          Sentry.captureException(error, {
            tags: {
              operation: 'dona_api_call',
              endpoint: 'invoice-file',
            },
            extra: {
              file_size: pdfBuffer.length,
              input_data: inputData,
            },
          });
        }

        throw error;
      }
    }
  );
}

/**
 * Process and match a tax invoice PDF file
 * @param pdfBuffer The PDF file as a buffer
 * @param inputData Optional input data for matching
 * @returns External service response
 */
export async function processTaxInvoiceFile(
  pdfBuffer: Buffer,
  inputData?: TaxInvoiceData
): Promise<ExternalTaxInvoiceResponse> {
  return await Sentry.startSpan(
    {
      op: 'http.client',
      name: 'DONA API - Process Tax Invoice File',
      attributes: {
        'external.service': 'DONA',
        'external.endpoint': '/documents/tax-invoice-file',
        'external.method': 'POST',
        'file.size_bytes': pdfBuffer.length,
        'input.vendor_name': inputData?.vendorName || '',
        'input.tax_invoice_number': inputData?.taxInvoiceNo || '',
        'input.has_amount': !!inputData?.invoiceAmount,
        'input.has_vat': !!inputData?.vatAmount,
      },
    },
    async (span) => {
      try {
        // Create a FormData object to send the file and input data
        const formData = new FormData();

        // Add the PDF file to the form data
        const blob = new Blob([pdfBuffer], { type: 'application/pdf' });
        formData.append('file', blob, 'tax-invoice.pdf');

        // Always add all required fields, even if empty
        formData.append('vendor_name', inputData?.vendorName || '');
        formData.append('tax_invoice_number', inputData?.taxInvoiceNo || '');

        // Format date if provided, otherwise empty string
        const dateStr = inputData?.invoiceDate
          ? (typeof inputData.invoiceDate === 'string'
              ? inputData.invoiceDate
              : inputData.invoiceDate.toISOString().split('T')[0])
          : '';
        formData.append('tax_invoice_date', dateStr);

        // Add numeric fields
        formData.append('invoice_amount', inputData?.invoiceAmount?.toString() || '');
        formData.append('vat_amount', inputData?.vatAmount?.toString() || '');

        // Add required boolean fields
        formData.append('show_confidence', 'true');
        formData.append('debug_timing', 'false');

        span.setAttribute('request.date_str', dateStr);
        span.setAttribute('request.form_fields_count', 7);

        // Make the API request
        const startTime = Date.now();
        const response = await fetch(`${DONA_HOST}/documents/tax-invoice-file`, {
          method: 'POST',
          headers: {
            'X-API-Key': DONA_API_KEY,
          },
          body: formData,
        });
        const requestDuration = Date.now() - startTime;

        span.setAttribute('http.status_code', response.status);
        span.setAttribute('http.response_time_ms', requestDuration);

        if (!response.ok) {
          const errorText = await response.text();
          span.setStatus({ code: 2, message: `HTTP ${response.status}` });
          span.setAttribute('error.response_body', errorText);

          const error = new Error(`API request failed with status ${response.status}: ${errorText}`);
          Sentry.captureException(error, {
            tags: {
              operation: 'dona_api_call',
              endpoint: 'tax-invoice-file',
            },
            extra: {
              status_code: response.status,
              response_body: errorText,
              file_size: pdfBuffer.length,
              input_data: inputData,
            },
          });
          throw error;
        }

        // Parse the response
        const data = await response.json();

        span.setStatus({ code: 1, message: 'Success' });
        span.setAttribute('response.processing_id', data.processing_id || 'unknown');
        span.setAttribute('response.has_results', !!data.results);

        if (data.results?.fields) {
          const fieldCount = Object.keys(data.results.fields).length;
          span.setAttribute('response.fields_count', fieldCount);
        }

        return data as ExternalTaxInvoiceResponse;
      } catch (error) {
        span.setStatus({ code: 2, message: error instanceof Error ? error.message : 'Unknown error' });
        span.setAttribute('error.type', error instanceof Error ? error.constructor.name : 'unknown');

        if (process.env.NODE_ENV !== 'test') {
          console.error('Error calling document processing service:', error);
        }

        // Re-capture if not already captured above
        if (!(error instanceof Error && error.message.includes('API request failed'))) {
          Sentry.captureException(error, {
            tags: {
              operation: 'dona_api_call',
              endpoint: 'tax-invoice-file',
            },
            extra: {
              file_size: pdfBuffer.length,
              input_data: inputData,
            },
          });
        }

        throw error;
      }
    }
  );
}

/**
 * Extract data from a standard invoice PDF (legacy method)
 * @param pdfBuffer The PDF file as a buffer
 * @returns Extracted invoice data
 */
export async function extractInvoiceData(pdfBuffer: Buffer): Promise<ExtractedInvoiceData> {
  // Call the new method and convert the response to the old format
  const response = await processInvoiceFile(pdfBuffer);

  return {
    vendorName: response.results.fields.vendor_name.ocr_value,
    invoiceNo: response.results.fields.invoice_number.ocr_value,
    invoiceDate: response.results.fields.invoice_date.ocr_value,
    invoiceAmount: parseFloat(response.results.fields.invoice_amount.ocr_value),
    vatAmount: parseFloat(response.results.fields.vat_amount.ocr_value),
  };
}

/**
 * Extract data from a tax invoice PDF (legacy method)
 * @param pdfBuffer The PDF file as a buffer
 * @returns Extracted tax invoice data
 */
export async function extractTaxInvoiceData(pdfBuffer: Buffer): Promise<ExtractedTaxInvoiceData> {
  // Call the new method and convert the response to the old format
  const response = await processTaxInvoiceFile(pdfBuffer);

  return {
    vendorName: response.results.fields.vendor_name.ocr_value,
    taxInvoiceNo: response.results.fields.tax_invoice_number.ocr_value,
    invoiceDate: response.results.fields.tax_invoice_date.ocr_value,
    invoiceAmount: parseFloat(response.results.fields.invoice_amount.ocr_value),
    vatAmount: parseFloat(response.results.fields.vat_amount.ocr_value),
  };
}

/**
 * Process and match a standard invoice from a URL
 * @param fileUrl URL to the PDF file
 * @param inputData Optional input data for matching
 * @returns External service response
 */
export async function processInvoice(
  fileUrl: string,
  inputData?: InvoiceData
): Promise<ExternalInvoiceResponse> {
  try {
    // Format date if provided, otherwise empty string
    const dateStr = inputData?.invoiceDate
      ? (typeof inputData.invoiceDate === 'string'
          ? inputData.invoiceDate
          : inputData.invoiceDate.toISOString().split('T')[0])
      : '';

    // Create a JSON payload with all required fields
    const payload = {
      file_url: fileUrl,
      vendor_name: inputData?.vendorName || '',
      invoice_number: inputData?.invoiceNo || '',
      invoice_date: dateStr,
      invoice_amount: inputData?.invoiceAmount ?? 0,
      vat_amount: inputData?.vatAmount ?? 0,
      show_confidence: true,
      debug_timing: false
    };

    // Make the API request
    const response = await fetch(`${DONA_HOST}/documents/invoice`, {
      method: 'POST',
      headers: {
        'X-API-Key': DONA_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${await response.text()}`);
    }

    // Parse the response
    const data = await response.json();
    return data as ExternalInvoiceResponse;
  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error calling document processing service:', error);
    }
    throw error;
  }
}

/**
 * Process and match a tax invoice from a URL
 * @param fileUrl URL to the PDF file
 * @param inputData Optional input data for matching
 * @returns External service response
 */
export async function processTaxInvoice(
  fileUrl: string,
  inputData?: TaxInvoiceData
): Promise<ExternalTaxInvoiceResponse> {
  try {
    // Format date if provided, otherwise empty string
    const dateStr = inputData?.invoiceDate
      ? (typeof inputData.invoiceDate === 'string'
          ? inputData.invoiceDate
          : inputData.invoiceDate.toISOString().split('T')[0])
      : '';

    // Create a JSON payload with all required fields
    const payload = {
      file_url: fileUrl,
      vendor_name: inputData?.vendorName || '',
      tax_invoice_number: inputData?.taxInvoiceNo || '',
      tax_invoice_date: dateStr,
      invoice_amount: inputData?.invoiceAmount?.toString() || '',
      vat_amount: inputData?.vatAmount?.toString() || '',
      show_confidence: true,
      debug_timing: false
    };

    // Make the API request
    const response = await fetch(`${DONA_HOST}/documents/tax-invoice`, {
      method: 'POST',
      headers: {
        'X-API-Key': DONA_API_KEY,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(`API request failed with status ${response.status}: ${await response.text()}`);
    }

    // Parse the response
    const data = await response.json();
    return data as ExternalTaxInvoiceResponse;
  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error calling document processing service:', error);
    }
    throw error;
  }
}
