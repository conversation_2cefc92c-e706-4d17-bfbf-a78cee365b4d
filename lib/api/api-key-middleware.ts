import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/db';
import crypto from 'crypto';
import { getToken } from 'next-auth/jwt';

/**
 * Result of API key validation
 */
export interface ApiKeyValidationResult {
  error?: NextResponse;
  apiKeyId?: string;
}

/**
 * Middleware to validate API key for API endpoints
 * @param req The incoming request
 * @returns ApiKeyValidationResult with error response or API key ID
 */
export async function validateApiKey(req: NextRequest): Promise<ApiKeyValidationResult> {
  // First, check if the request has a valid session
  const token = await getToken({
    req,
    secret: process.env.NEXTAUTH_SECRET
  });

  // If there's a valid session, allow the request without an API key
  if (token) {
    return {}; // User is authenticated via session, allow the request
  }

  // If no session, check for API key
  const apiKey = req.headers.get('X-API-Key');

  // If no API key is provided, return 401 Unauthorized
  if (!apiKey) {
    return {
      error: NextResponse.json(
        { error: 'API key is required' },
        { status: 401 }
      )
    };
  }

  try {
    // Hash the provided API key for lookup
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

    // Find the API key hash in the database
    const apiKeyRecord = await prisma.apiKey.findFirst({
      where: {
        keyHash: hashedKey,
        isActive: true,
      },
    });

    // If the API key is not found or not active, return 401 Unauthorized
    if (!apiKeyRecord) {
      return {
        error: NextResponse.json(
          { error: 'Invalid API key' },
          { status: 401 }
        )
      };
    }

    // Update the lastUsedAt timestamp
    await prisma.apiKey.update({
      where: {
        id: apiKeyRecord.id,
      },
      data: {
        lastUsedAt: new Date(),
      },
    });
    // Return the API key ID
    return { apiKeyId: apiKeyRecord.id };
  } catch (error) {
    if (process.env.NODE_ENV !== 'test') {
      console.error('Error validating API key:', error);
    }
    return {
      error: NextResponse.json(
        { error: 'Error validating API key' },
        { status: 500 }
      )
    };
  }
}
