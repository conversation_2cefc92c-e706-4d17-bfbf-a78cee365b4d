# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules

# next.js
/.next/
/out/

# production
/build

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE specific files
.idea/
.vscode/
*.swp
*.swo

# Logs
logs
*.log
!app/api/logs

# Cache
.cache/
.eslintcache

# Test coverage
/coverage/

# Generated files
/public/sitemap*.xml
/public/robots.txt
/public/sw.js
/public/workbox-*.js
lib/api/*.js

# Temporary files
/temp/
/tmp/

# Mac
.DS_Store

# Sentry Config File
.env.sentry-build-plugin

# Test configuration files
# Keep test-config.json (test cases) but ignore test-env.json (environment variables)
/tests/integration/test-env.json
