import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/db';
import { format } from 'date-fns';
import { LogFilterOptions, PaginationOptions, LogsResponse } from '@/lib/types/log';

/**
 * GET /api/logs
 * Fetch processing logs with optional filtering and pagination
 */
export async function GET(req: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(req.url);

    // Parse filter options
    const filters: LogFilterOptions = {
      requestType: searchParams.get('requestType') || undefined,
      processingStatus: searchParams.get('processingStatus') || undefined,
      vendorName: searchParams.get('vendorName') || undefined,
      documentNo: searchParams.get('documentNo') || undefined,
    };

    // Parse date range
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    if (startDateParam) {
      filters.startDate = new Date(startDateParam);
    }

    if (endDateParam) {
      filters.endDate = new Date(endDateParam);
    }

    // Parse pagination options
    const pagination: PaginationOptions = {
      page: parseInt(searchParams.get('page') || '1', 10),
      pageSize: parseInt(searchParams.get('pageSize') || '10', 10),
    };

    // Build the where clause for filtering
    const where: any = {};

    if (filters.requestType) {
      where.requestType = filters.requestType;
    }

    if (filters.processingStatus) {
      where.processingStatus = filters.processingStatus;
    }

    if (filters.vendorName) {
      where.inputVendorName = {
        contains: filters.vendorName,
        mode: 'insensitive',
      };
    }

    if (filters.documentNo) {
      where.inputDocumentNo = {
        contains: filters.documentNo,
        mode: 'insensitive',
      };
    }

    // Add date range filter
    if (filters.startDate || filters.endDate) {
      where.requestTimestamp = {};

      if (filters.startDate) {
        where.requestTimestamp.gte = filters.startDate;
      }

      if (filters.endDate) {
        // Add one day to include the end date fully
        const endDate = new Date(filters.endDate);
        endDate.setDate(endDate.getDate() + 1);
        where.requestTimestamp.lt = endDate;
      }
    }

    // Count total logs matching the filter
    const totalLogs = await prisma.processingLog.count({ where });

    // Calculate total pages
    const totalPages = Math.ceil(totalLogs / pagination.pageSize);

    // Fetch logs with pagination and include API key information
    const logs = await prisma.processingLog.findMany({
      where,
      orderBy: {
        requestTimestamp: 'desc',
      },
      skip: (pagination.page - 1) * pagination.pageSize,
      take: pagination.pageSize,
      include: {
        apiKey: {
          select: {
            name: true
          }
        }
      }
    });

    // Format dates for display
    const formattedLogs = logs.map(log => ({
      ...log,
      requestTimestamp: format(log.requestTimestamp, 'yyyy-MM-dd HH:mm:ss'),
      inputDocumentDate: log.inputDocumentDate ? format(log.inputDocumentDate, 'yyyy-MM-dd') : null,
      ocrDocumentDate: log.ocrDocumentDate ? format(log.ocrDocumentDate, 'yyyy-MM-dd') : null,
      processedAt: log.processedAt ? format(log.processedAt, 'yyyy-MM-dd HH:mm:ss') : null,
      // Calculate matched fields percentage for display
      matchedFieldsPercentage: log.totalFields && log.totalFields > 0
        ? Math.round((log.matchedFields || 0) / log.totalFields * 100)
        : null,
      // Add API key name if available
      apiKeyName: log.apiKey?.name || null,
      // Remove the apiKey object from the response
      apiKey: undefined,
    }));

    // Prepare response
    const response: LogsResponse = {
      logs: formattedLogs,
      total: totalLogs,
      page: pagination.page,
      pageSize: pagination.pageSize,
      totalPages,
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching logs:', error);
    return NextResponse.json(
      { error: 'Failed to fetch logs' },
      { status: 500 }
    );
  }
}
